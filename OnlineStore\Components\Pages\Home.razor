﻿@page "/"

<PageTitle>الرئيسية - متجر إلكتروني</PageTitle>

<!-- Hero Section -->
<div class="hero-section slide-up">
    <div class="hero-content">
        <MudText Typo="Typo.h2" Class="hero-title">مرحباً بك في متجرنا الإلكتروني</MudText>
        <MudText Typo="Typo.h6" Class="hero-subtitle">
            اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار
        </MudText>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Secondary"
                   Size="Size.Large"
                   Class="btn-secondary-custom mr-3"
                   Href="/customer">
            ابدأ التسوق الآن
        </MudButton>
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Surface"
                   Size="Size.Large"
                   Href="/order">
            عرض الطلبات
        </MudButton>
    </div>
</div>

<!-- Features Section -->
<MudContainer MaxWidth="MaxWidth.Large" Class="mt-8">
    <MudText Typo="Typo.h4" Align="Align.Center" Class="mb-6" Color="Color.Primary">
        لماذا تختار متجرنا؟
    </MudText>

    <MudGrid>
        <MudItem xs="12" md="4" Class="fade-in">
            <div class="feature-card">
                <div class="feature-icon">
                    <MudIcon Icon="@Icons.Material.Filled.LocalShipping" />
                </div>
                <MudText Typo="Typo.h6" Class="mb-2">شحن سريع</MudText>
                <MudText Typo="Typo.body2" Class="mud-text-secondary">
                    توصيل سريع وآمن لجميع أنحاء المملكة خلال 24-48 ساعة
                </MudText>
            </div>
        </MudItem>

        <MudItem xs="12" md="4" Class="fade-in">
            <div class="feature-card">
                <div class="feature-icon">
                    <MudIcon Icon="@Icons.Material.Filled.Security" />
                </div>
                <MudText Typo="Typo.h6" Class="mb-2">دفع آمن</MudText>
                <MudText Typo="Typo.body2" Class="mud-text-secondary">
                    نظام دفع آمن ومشفر يحمي بياناتك المالية بأعلى معايير الأمان
                </MudText>
            </div>
        </MudItem>

        <MudItem xs="12" md="4" Class="fade-in">
            <div class="feature-card">
                <div class="feature-icon">
                    <MudIcon Icon="@Icons.Material.Filled.SupportAgent" />
                </div>
                <MudText Typo="Typo.h6" Class="mb-2">دعم 24/7</MudText>
                <MudText Typo="Typo.body2" Class="mud-text-secondary">
                    فريق دعم فني متاح على مدار الساعة لمساعدتك في أي استفسار
                </MudText>
            </div>
        </MudItem>
    </MudGrid>
</MudContainer>

<!-- Statistics Section -->
<MudContainer MaxWidth="MaxWidth.Large" Class="mt-8">
    <MudCard Class="custom-card">
        <MudCardContent Class="pa-6">
            <MudText Typo="Typo.h5" Align="Align.Center" Class="mb-4" Color="Color.Primary">
                إحصائيات المتجر
            </MudText>
            <MudGrid>
                <MudItem xs="6" md="3" Class="text-center">
                    <MudText Typo="Typo.h4" Color="Color.Primary">1000+</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">منتج متاح</MudText>
                </MudItem>
                <MudItem xs="6" md="3" Class="text-center">
                    <MudText Typo="Typo.h4" Color="Color.Secondary">500+</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">عميل راضي</MudText>
                </MudItem>
                <MudItem xs="6" md="3" Class="text-center">
                    <MudText Typo="Typo.h4" Color="Color.Tertiary">50+</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">مدينة نخدمها</MudText>
                </MudItem>
                <MudItem xs="6" md="3" Class="text-center">
                    <MudText Typo="Typo.h4" Color="Color.Info">99%</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">رضا العملاء</MudText>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>
</MudContainer>

<!-- Call to Action Section -->
<MudContainer MaxWidth="MaxWidth.Large" Class="mt-8 mb-8">
    <MudCard Class="custom-card">
        <div class="custom-card-header text-center">
            <MudText Typo="Typo.h5" Class="mb-2">ابدأ رحلة التسوق معنا اليوم</MudText>
            <MudText Typo="Typo.body1">انضم إلى آلاف العملاء الراضين واستمتع بتجربة تسوق فريدة</MudText>
        </div>
        <MudCardContent Class="text-center">
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       Size="Size.Large"
                       Class="btn-primary-custom mr-3"
                       Href="/customer">
                <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="mr-2" />
                إنشاء حساب جديد
            </MudButton>
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Primary"
                       Size="Size.Large"
                       Href="/order">
                <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Class="mr-2" />
                تصفح المنتجات
            </MudButton>
        </MudCardContent>
    </MudCard>
</MudContainer>
