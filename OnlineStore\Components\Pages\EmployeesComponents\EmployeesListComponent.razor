@using OnlineStore.Components.Pages.Dialogs
@using OnlineStore.Data.Entities
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@rendermode InteractiveServer

<p> @AdministrationId</p>

<MudTable Items="FilteredEmployees"
          Hover="true"
          Loading="loading"
          LoadingProgressColor="Color.Info"
          Dense="true">
    <ToolBarContent>
        <MudSelect T="bool?" @bind-Value="showActiveOnly" Label="????? ????????" Variant="Variant.Outlined" Margin="Margin.Dense" Style="min-width: 150px;">
            <MudSelectItem Value="@((bool?)null)">??? ????????</MudSelectItem>
            <MudSelectItem Value="@((bool?)true)">??????? ????</MudSelectItem>
            <MudSelectItem Value="@((bool?)false)">??????? ??? ???????</MudSelectItem>
        </MudSelect>
        <MudSpacer />
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Add"
                   @onclick="OpenAddEmployeeDialog">????? ????</MudButton>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>????? ???????</MudTh>
        <MudTh>????? ??????</MudTh>
        <MudTh>????? (????)</MudTh>
        <MudTh>????? (???????)</MudTh>
        <MudTh>??? ??????</MudTh>
        <MudTh>?????? ??????????</MudTh>
        <MudTh>??????</MudTh>
        <MudTh>????? ???????</MudTh>
        <MudTh>????? ???????</MudTh>
        <MudTh>???????</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="????? ???????">@context.EmployementNumber</MudTd>
        <MudTd DataLabel="????? ??????">@context.NID</MudTd>
        <MudTd DataLabel="????? (????)">@context.NameAr</MudTd>
        <MudTd DataLabel="????? (???????)">@context.NameEng</MudTd>
        <MudTd DataLabel="??? ??????">@context.Phone</MudTd>
        <MudTd DataLabel="?????? ??????????">@context.Email</MudTd>
        <MudTd DataLabel="??????">
            @if (context.IsActive)
            {
                <MudChip T="string" Color="Color.Success" Size="Size.Small">???</MudChip>
            }
            else
            {
                <MudChip T="string" Color="Color.Error" Size="Size.Small">??? ???</MudChip>
            }
        </MudTd>
        <MudTd DataLabel="????? ???????">@context.CreatedDate.ToString("yyyy/MM/dd HH:mm")</MudTd>
        <MudTd DataLabel="????? ???????">@(context.UpdatedDate?.ToString("yyyy/MM/dd HH:mm") ?? "-")</MudTd>
        <MudTd>
            <MudTooltip Text="?????">
                <MudIconButton Color="Color.Primary" Icon="@Icons.Material.Filled.Edit" OnClick="() => OpenEditDialog(context)" />
            </MudTooltip>
            <MudTooltip Text="???">
                <MudIconButton Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="() => DeleteEmployee(context.Id)" />
            </MudTooltip>
        </MudTd>
    </RowTemplate>
</MudTable>



@code {
    [CascadingParameter(Name = "AdministrationId")] public Guid AdministrationId { get; set; }


    public List<Employee> Employees = new();
    private bool loading = false;
    private Employee selectedEmployee = new();
    private bool? showActiveOnly = null;

    private List<Employee> FilteredEmployees => showActiveOnly switch
    {
        true => Employees.Where(e => e.IsActive).ToList(),
        false => Employees.Where(e => !e.IsActive).ToList(),
        null => Employees
    };

    protected override async Task OnParametersSetAsync()
    {
        await LoadEmployees();

    }

    private void OpenAddEmployeeDialog()
    {
        selectedEmployee = new Employee { AdministrationId = AdministrationId };

        var parameteres = new DialogParameters
        {
            ["EmployeeId"] = null,
            ["AdministrationId"] = AdministrationId,
            ["OnSave"] = EventCallback.Factory.Create(this, OnDialogSave)
        };

        DialogService.ShowAsync<EmployeeEditComponent>(
            "????? ????",
            parameteres,
            new DialogOptions { MaxWidth = MaxWidth.Medium }
        );

    }

    private void OpenEditDialog(Employee employee)
    {
        selectedEmployee = employee;
        var parameters = new DialogParameters
        {
            ["EmployeeId"] = employee.Id,
            ["AdministrationId"] = employee.AdministrationId,
            ["OnSave"] = EventCallback.Factory.Create(this, OnDialogSave)
        };

        DialogService.ShowAsync<EmployeeEditComponent>("????? ?????? ????",
        parameters,
        new DialogOptions { MaxWidth = MaxWidth.Small });
    }
    private async Task LoadEmployees()
    {
        loading = true;
        Employees = await Http.GetFromJsonAsync<List<Employee>>($"/api/employees/by-administration/{AdministrationId}") ?? new();
        loading = false;
    }


    private async Task OnDialogSave()
    {
        await LoadEmployees();
    }
    private async Task DeleteEmployee(Guid employeeId)
    {
        var parameters = new DialogParameters
        {
            { "ContentText", "?? ???? ??? ?????? ??? ???? ?? ??? ???? ?? ????? ???????? ?? ?????? ?? ??????? ???." },
            { "ConfirmationText", "???" },
            { "CancelText", "??" },
            { "Color", Color.Error }
        };

        var _dialogOptions = new DialogOptions() { MaxWidth = MaxWidth.Medium, BackdropClick = false };
        var msg = $"????? ???? ??????: {selectedEmployee.NameAr}";
        var dialog = await DialogService.ShowAsync<ConfirmDelete>(msg, parameters, _dialogOptions);
        var result = await dialog.Result;

        if (result != null && !result.Canceled)
        {
            // ???? ?????? ??? ??? ????? ?? ?????
            var employee = Employees.FirstOrDefault(e => e.Id == employeeId);
            if (employee != null)
            {
                employee.IsActive = false;
                employee.UpdatedDate = DateTime.Now;
                await Http.PutAsJsonAsync($"/api/employees/{employee.Id}", employee);
                await LoadEmployees();
                Snackbar.Add("?? ??? ?????? ??? ??? ??? ???? ?? ???????.", Severity.Success);
            }
        }
    }
}
