﻿@page "/AdministrationList"
@inject NavigationManager NavigationManager
@inject HttpClient Http
@inject ISnackbar snackbar
@inject IDialogService DialogService

@rendermode InteractiveServer
@using OnlineStore.Components.Pages.Dialogs
@using OnlineStore.Data.Entities

<MudTable Items="Administrations"
          Hover="true"
          Loading="@_loading"
          LoadingProgressColor="Color.Info"
          Dense="true">
    <ToolBarContent>
        <MudSpacer />
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Add"
                   @onclick="NavigateToAddAdministration">إضافة</MudButton>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>رقم معرف</MudTh>
        <MudTh>الإسم</MudTh>
        <MudTh>الوصف</MudTh>
        <MudTh>العمليات</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="رقم معرف">@context.Id</MudTd>
        <MudTd DataLabel="الاسم">@context.Name</MudTd>
        <MudTd DataLabel="الوصف">@context.Description</MudTd>
        <MudTd>
            <MudTooltip Text="تعديل">
                <MudIconButton Color="Color.Primary"
                               Icon="@Icons.Material.Filled.Edit"
                               OnClick="()=>NavigateToAddAdministration(context)" />
            </MudTooltip>
            <MudTooltip Text="حذف">
                <MudIconButton Color="Color.Error"
                               Icon="@Icons.Material.Filled.Delete"
                               OnClick="()=> DeleteAdministration(context)" />
            </MudTooltip>
        </MudTd>
    </RowTemplate>
</MudTable>

@code {
    private bool _loading = false;
    public List<Administration> Administrations { get; set; } = new List<Administration>();

    protected override async Task OnInitializedAsync()
    {
        _loading = true;
        Administrations = await Http.GetFromJsonAsync<List<Administration>>("/api/administrations");
        _loading = false;
    }

    public void NavigateToAddAdministration()
    {
        NavigationManager.NavigateTo("/AdministrationEdit");
    }

    public void NavigateToAddAdministration(Administration administration)
    {
        NavigationManager.NavigateTo($"/AdministrationEdit/{administration.Id}");
    }

    public async Task DeleteAdministration(Administration administration)
    {
        // تحقق من وجود موظفين مرتبطين بالإدارة
        var employees = await Http.GetFromJsonAsync<List<Employee>>($"/api/employees/by-administration/{administration.Id}");
        if (employees != null && employees.Count > 0)
        {
            var parameters = new DialogParameters
            {
                { "ContentText", "يوجد موظفين مرتبطين بهذه الإدارة. إذا قمت بحذف الإدارة سيتم حذف جميع الموظفين المرتبطين بها. هل تريد المتابعة؟" },
                { "ConfirmationText", "نعم" },
                { "CancelText", "لا" },
                { "Color", Color.Warning }
            };
            var _dialogOptions = new DialogOptions() { MaxWidth = MaxWidth.Medium, BackdropClick = false };
            var dialog = await DialogService.ShowAsync<ConfirmDelete>("تحذير", parameters, _dialogOptions);
            var result = await dialog.Result;
            if (result == null || result.Canceled)
                return;
        }

        var confirmParameters = new DialogParameters
        {
            { "ContentText", "هل أنت متأكد من عملية الحذف؟" },
            { "ConfirmationText", "حذف" },
            { "CancelText", "إلغاء" },
            { "Color", Color.Error }
        };
        var confirmDialogOptions = new DialogOptions() { MaxWidth = MaxWidth.Medium, BackdropClick = false };
        var msg = $"سيتم حذف الإدارة: {administration.Name}";
        var dialog2 = await DialogService.ShowAsync<ConfirmDelete>(msg, confirmParameters, confirmDialogOptions);
        var result2 = await dialog2.Result;
        if (result2 != null && !result2.Canceled)
        {
            var response = await Http.DeleteAsync($"/api/administrations/{administration.Id}");
            if (response.IsSuccessStatusCode)
            {
                Administrations.Remove(administration);
                snackbar.Add("تم حذف الإدارة بنجاح", Severity.Success);
            }
            else
            {
                snackbar.Add("حدث خطأ أثناء حذف الإدارة", Severity.Error);
            }
        }
    }
}
