using OnlineStore.Data.Entities;

namespace OnlineStore.Services
{
    public class CustomerService
    {
        private Customer? _currentCustomer;

        public Customer? CurrentCustomer => _currentCustomer;

        public bool IsLoggedIn => _currentCustomer != null;

        public event Action? OnCustomerChanged;

        public void SetCurrentCustomer(Customer? customer)
        {
            _currentCustomer = customer;
            OnCustomerChanged?.Invoke();
        }

        public void Logout()
        {
            _currentCustomer = null;
            OnCustomerChanged?.Invoke();
        }
    }
}
