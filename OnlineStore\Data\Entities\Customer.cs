namespace OnlineStore.Data.Entities
{
    public class Customer
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public List<Order> Orders { get; set; } = [];

        public void PlaceOrder()
        {
            // Method implementation for placing order
        }
    }
}
