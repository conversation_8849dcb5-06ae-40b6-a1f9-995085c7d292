@page "/products"
@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using OnlineStore.Components.Pages.Dialogs
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject IDialogService DialogService
@rendermode InteractiveServer

<PageTitle>المنتجات - متجر إلكتروني</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <!-- Page Header -->
    <div class="products-header slide-up mb-6">
        <MudCard Class="custom-card">
            <div class="custom-card-header">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Inventory" Size="Size.Large" Class="mr-4" />
                    <div>
                        <MudText Typo="Typo.h4" Class="mb-1">إدارة المنتجات</MudText>
                        <MudText Typo="Typo.body1" Class="opacity-90">إضافة وإدارة منتجات المتجر</MudText>
                    </div>
                    <MudSpacer />
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Primary" 
                               Class="btn-primary-custom"
                               OnClick="OpenAddProductDialog">
                        <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-2" />
                        إضافة منتج جديد
                    </MudButton>
                </div>
            </div>
        </MudCard>
    </div>

    <!-- Products Statistics -->
    <MudGrid Class="mb-6">
        <MudItem xs="12" md="3" Class="fade-in">
            <MudCard Class="stat-card">
                <MudCardContent Class="text-center pa-4">
                    <div class="stat-icon total">
                        <MudIcon Icon="@Icons.Material.Filled.Inventory" />
                    </div>
                    <MudText Typo="Typo.h5" Class="mt-2">@products.Count</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">إجمالي المنتجات</MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" md="3" Class="fade-in">
            <MudCard Class="stat-card">
                <MudCardContent Class="text-center pa-4">
                    <div class="stat-icon completed">
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" />
                    </div>
                    <MudText Typo="Typo.h5" Class="mt-2">@products.Count(p => p.IsActive)</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">منتجات نشطة</MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" md="3" Class="fade-in">
            <MudCard Class="stat-card">
                <MudCardContent Class="text-center pa-4">
                    <div class="stat-icon processing">
                        <MudIcon Icon="@Icons.Material.Filled.Inventory2" />
                    </div>
                    <MudText Typo="Typo.h5" Class="mt-2">@products.Sum(p => p.Stock)</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">إجمالي المخزون</MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" md="3" Class="fade-in">
            <MudCard Class="stat-card">
                <MudCardContent Class="text-center pa-4">
                    <div class="stat-icon pending">
                        <MudIcon Icon="@Icons.Material.Filled.Warning" />
                    </div>
                    <MudText Typo="Typo.h5" Class="mt-2">@products.Count(p => p.Stock < 10)</MudText>
                    <MudText Typo="Typo.body2" Class="mud-text-secondary">مخزون منخفض</MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- Search and Filter -->
    <MudCard Class="custom-card mb-6 fade-in">
        <MudCardContent>
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="searchText" 
                                  Label="البحث في المنتجات" 
                                  Variant="Variant.Outlined"
                                  Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Search"
                                  OnKeyUp="FilterProducts" />
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudSelect @bind-Value="selectedCategory"
                               Label="الفئة"
                               Variant="Variant.Outlined"
                               T="string"
                               SelectedValueChanged="@((string value) => { selectedCategory = value; FilterProducts(); })">
                        <MudSelectItem Value="@("")">جميع الفئات</MudSelectItem>
                        @foreach (var category in categories)
                        {
                            <MudSelectItem Value="@category">@category</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudSelect @bind-Value="statusFilter"
                               Label="الحالة"
                               Variant="Variant.Outlined"
                               T="string"
                               SelectedValueChanged="@((string value) => { statusFilter = value; FilterProducts(); })">
                        <MudSelectItem Value="@("")">جميع الحالات</MudSelectItem>
                        <MudSelectItem Value="@("active")">نشط</MudSelectItem>
                        <MudSelectItem Value="@("inactive")">غير نشط</MudSelectItem>
                    </MudSelect>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- Products Grid -->
    <MudGrid>
        @foreach (var product in filteredProducts)
        {
            <MudItem xs="12" sm="6" md="4" lg="3" Class="fade-in">
                <MudCard Class="product-card hover-lift">
                    <MudCardMedia Image="@(!string.IsNullOrEmpty(product.ImageUrl) ? product.ImageUrl : "/images/no-image.png")" 
                                  Height="200" />
                    <MudCardContent>
                        <div class="d-flex justify-space-between align-center mb-2">
                            <MudText Typo="Typo.h6" Class="product-name">@product.Name</MudText>
                            <MudChip T="string" Size="Size.Small" 
                                     Color="@(product.IsActive ? Color.Success : Color.Error)">
                                @(product.IsActive ? "نشط" : "غير نشط")
                            </MudChip>
                        </div>
                        
                        <MudText Typo="Typo.body2" Class="mud-text-secondary mb-3 product-description">
                            @(product.Description.Length > 100 ? product.Description.Substring(0, 100) + "..." : product.Description)
                        </MudText>
                        
                        <div class="d-flex justify-space-between align-center mb-3">
                            <MudText Typo="Typo.h6" Color="Color.Primary">@product.Price.ToString("C")</MudText>
                            <MudText Typo="Typo.body2" Class="@(product.Stock < 10 ? "text-error" : "mud-text-secondary")">
                                المخزون: @product.Stock
                            </MudText>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(product.Category))
                        {
                            <MudChip T="string" Size="Size.Small" Color="Color.Info" Class="mb-3">@product.Category</MudChip>
                        }
                    </MudCardContent>
                    
                    <MudCardActions>
                        <MudButton Variant="Variant.Text" 
                                   Color="Color.Primary"
                                   OnClick="() => EditProduct(product)">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Size="Size.Small" Class="mr-1" />
                            تعديل
                        </MudButton>
                        <MudButton Variant="Variant.Text" 
                                   Color="@(product.IsActive ? Color.Warning : Color.Success)"
                                   OnClick="() => ToggleProductStatus(product)">
                            <MudIcon Icon="@(product.IsActive ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)" 
                                     Size="Size.Small" Class="mr-1" />
                            @(product.IsActive ? "إخفاء" : "إظهار")
                        </MudButton>
                        <MudButton Variant="Variant.Text" 
                                   Color="Color.Error"
                                   OnClick="() => DeleteProduct(product)">
                            <MudIcon Icon="@Icons.Material.Filled.Delete" Size="Size.Small" Class="mr-1" />
                            حذف
                        </MudButton>
                    </MudCardActions>
                </MudCard>
            </MudItem>
        }
    </MudGrid>

    @if (!filteredProducts.Any())
    {
        <div class="empty-state pa-8 text-center">
            <MudIcon Icon="@Icons.Material.Filled.Inventory" Size="Size.Large" Class="mb-4 mud-text-secondary" />
            <MudText Typo="Typo.h6" Class="mb-2 mud-text-secondary">لا توجد منتجات</MudText>
            <MudText Typo="Typo.body2" Class="mud-text-secondary mb-4">
                @(products.Any() ? "لا توجد منتجات تطابق معايير البحث" : "ابدأ بإضافة منتجك الأول")
            </MudText>
            @if (!products.Any())
            {
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           OnClick="OpenAddProductDialog">
                    <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-2" />
                    إضافة منتج جديد
                </MudButton>
            }
        </div>
    }
</MudContainer>

@code {
    private List<Product> products = new();
    private List<Product> filteredProducts = new();
    private List<string> categories = new();
    private string searchText = "";
    private string selectedCategory = "";
    private string statusFilter = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        products = await DbContext.Products
            .OrderByDescending(p => p.CreatedDate)
            .ToListAsync();

        categories = products
            .Where(p => !string.IsNullOrEmpty(p.Category))
            .Select(p => p.Category)
            .Distinct()
            .OrderBy(c => c)
            .ToList();

        FilterProducts();
    }

    private void FilterProducts()
    {
        filteredProducts = products.Where(p =>
            (string.IsNullOrEmpty(searchText) ||
             p.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
             p.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(selectedCategory) || p.Category == selectedCategory) &&
            (string.IsNullOrEmpty(statusFilter) ||
             (statusFilter == "active" && p.IsActive) ||
             (statusFilter == "inactive" && !p.IsActive))
        ).ToList();

        StateHasChanged();
    }

    private async Task OpenAddProductDialog()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Medium, FullWidth = true };

        var dialog = await DialogService.ShowAsync<AddProductDialog>("إضافة منتج جديد", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadProducts();
            FilterProducts();
            await JSRuntime.InvokeVoidAsync("alert", "تم إضافة المنتج بنجاح!");
        }
    }

    private async Task EditProduct(Product product)
    {
        var parameters = new DialogParameters<EditProductDialog>
        {
            { x => x.ProductToEdit, product }
        };
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Medium, FullWidth = true };

        var dialog = await DialogService.ShowAsync<EditProductDialog>("تعديل المنتج", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadProducts();
            FilterProducts();
            await JSRuntime.InvokeVoidAsync("alert", "تم تحديث المنتج بنجاح!");
        }
    }

    private async Task ToggleProductStatus(Product product)
    {
        product.IsActive = !product.IsActive;
        product.UpdatedDate = DateTime.Now;

        DbContext.Products.Update(product);
        await DbContext.SaveChangesAsync();

        await JSRuntime.InvokeVoidAsync("alert", $"تم {(product.IsActive ? "تفعيل" : "إلغاء تفعيل")} المنتج بنجاح!");
        FilterProducts();
    }

    private async Task DeleteProduct(Product product)
    {
        bool confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل أنت متأكد من حذف المنتج '{product.Name}'؟");

        if (confirmed)
        {
            try
            {
                DbContext.Products.Remove(product);
                await DbContext.SaveChangesAsync();

                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المنتج بنجاح!");
                await LoadProducts();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", "لا يمكن حذف هذا المنتج لأنه مرتبط بطلبات موجودة.");
            }
        }
    }
}
