@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using Microsoft.EntityFrameworkCore
@using MudBlazor
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 400px;">
            <MudText Typo="Typo.h6" Class="mb-4">Customer Login</MudText>
            
            <MudTextField @bind-Value="email" 
                          Label="Email" 
                          Variant="Variant.Outlined" 
                          Required="true"
                          Class="mb-3" />
            
            <MudTextField @bind-Value="password" 
                          Label="Password" 
                          Variant="Variant.Outlined" 
                          InputType="InputType.Password"
                          Required="true"
                          Class="mb-3" />

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <MudAlert Severity="Severity.Error" Class="mb-3">@errorMessage</MudAlert>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Cancel</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="Login">Login</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = default!;

    private string email = string.Empty;
    private string password = string.Empty;
    private string errorMessage = string.Empty;

    private async Task Login()
    {
        errorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(email) || string.IsNullOrWhiteSpace(password))
        {
            errorMessage = "Please enter both email and password.";
            return;
        }

        try
        {
            var customer = await DbContext.Customers
                .Include(c => c.Orders)
                .FirstOrDefaultAsync(c => c.Email == email && c.Password == password);

            if (customer == null)
            {
                errorMessage = "Invalid email or password.";
                return;
            }

            MudDialog.Close(DialogResult.Ok(customer));
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            await JSRuntime.InvokeVoidAsync("console.error", ex.Message);
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
