@page "/order"
@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using OnlineStore.Services
@using Microsoft.EntityFrameworkCore
@using OrderEntity = OnlineStore.Data.Entities.Order
@using CustomerEntity = OnlineStore.Data.Entities.Customer
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject CustomerService CustomerService
@rendermode InteractiveServer

<PageTitle>الطلبات - متجر إلكتروني</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    @if (!isCustomerLoggedIn)
    {
        <!-- Login Required Section -->
        <div class="hero-section slide-up mb-6">
            <div class="hero-content">
                <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Class="mb-3" />
                <MudText Typo="Typo.h3" Class="hero-title">تسجيل الدخول مطلوب</MudText>
                <MudText Typo="Typo.h6" Class="hero-subtitle">
                    يجب تسجيل الدخول كعميل للوصول إلى طلباتك وإدارتها
                </MudText>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Secondary"
                           Size="Size.Large"
                           Class="btn-secondary-custom"
                           Href="/customer">
                    <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" />
                    الذهاب لتسجيل الدخول
                </MudButton>
            </div>
        </div>

        <!-- Benefits of Having Account -->
        <MudContainer MaxWidth="MaxWidth.Large">
            <MudText Typo="Typo.h5" Align="Align.Center" Class="mb-6" Color="Color.Primary">
                مزايا إدارة الطلبات
            </MudText>

            <MudGrid>
                <MudItem xs="12" md="4" Class="fade-in">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <MudIcon Icon="@Icons.Material.Filled.TrackChanges" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">تتبع الطلبات</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            تابع حالة طلباتك من الطلب حتى التسليم
                        </MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" md="4" Class="fade-in">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <MudIcon Icon="@Icons.Material.Filled.History" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">سجل الطلبات</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            عرض جميع طلباتك السابقة والحالية
                        </MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" md="4" Class="fade-in">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <MudIcon Icon="@Icons.Material.Filled.Cancel" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">إلغاء الطلبات</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            إمكانية إلغاء الطلبات قبل المعالجة
                        </MudText>
                    </div>
                </MudItem>
            </MudGrid>
        </MudContainer>
    }
    else
    {
        <!-- Page Header -->
        <div class="orders-header slide-up mb-6">
            <MudCard Class="custom-card">
                <div class="custom-card-header">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Size="Size.Large" Class="mr-4" />
                        <div>
                            <MudText Typo="Typo.h4" Class="mb-1">إدارة الطلبات</MudText>
                            <MudText Typo="Typo.body1" Class="opacity-90">مرحباً، @currentCustomer?.Name!</MudText>
                        </div>
                        <MudSpacer />
                        <MudChip T="string" Color="Color.Info" Size="Size.Medium">
                            <MudIcon Icon="@Icons.Material.Filled.ShoppingBag" Size="Size.Small" Class="mr-1" />
                            @orders.Count طلب
                        </MudChip>
                    </div>
                </div>
            </MudCard>
        </div>

        <!-- Order Statistics -->
        <MudGrid Class="mb-6">
            <MudItem xs="12" md="3" Class="fade-in">
                <MudCard Class="stat-card">
                    <MudCardContent Class="text-center pa-4">
                        <div class="stat-icon pending">
                            <MudIcon Icon="@Icons.Material.Filled.Schedule" />
                        </div>
                        <MudText Typo="Typo.h5" Class="mt-2">@orders.Count(o => o.Status == "Pending")</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">قيد الانتظار</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" md="3" Class="fade-in">
                <MudCard Class="stat-card">
                    <MudCardContent Class="text-center pa-4">
                        <div class="stat-icon processing">
                            <MudIcon Icon="@Icons.Material.Filled.Autorenew" />
                        </div>
                        <MudText Typo="Typo.h5" Class="mt-2">@orders.Count(o => o.Status == "Processing")</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">قيد المعالجة</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" md="3" Class="fade-in">
                <MudCard Class="stat-card">
                    <MudCardContent Class="text-center pa-4">
                        <div class="stat-icon completed">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" />
                        </div>
                        <MudText Typo="Typo.h5" Class="mt-2">@orders.Count(o => o.Status == "Completed")</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">مكتملة</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" md="3" Class="fade-in">
                <MudCard Class="stat-card">
                    <MudCardContent Class="text-center pa-4">
                        <div class="stat-icon total">
                            <MudIcon Icon="@Icons.Material.Filled.AttachMoney" />
                        </div>
                        <MudText Typo="Typo.h5" Class="mt-2">@orders.Sum(o => o.Total).ToString("C")</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">إجمالي المبلغ</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Shopping Cart Section -->
        <MudCard Class="custom-card mb-6 fade-in">
            <div class="custom-card-header">
                <MudText Typo="Typo.h5">سلة التسوق</MudText>
            </div>
            <MudCardContent Class="custom-card-content">
                @if (cartItems.Any())
                {
                    @foreach (var item in cartItems)
                    {
                        <div class="cart-item d-flex align-center justify-space-between pa-3 mb-2">
                            <div class="d-flex align-center">
                                <img src="@(!string.IsNullOrEmpty(item.Product.ImageUrl) ? item.Product.ImageUrl : "/images/no-image.png")"
                                     alt="@item.Product.Name"
                                     class="cart-item-image mr-3" />
                                <div>
                                    <MudText Typo="Typo.h6">@item.Product.Name</MudText>
                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">@item.Product.Price.ToString("C") / قطعة</MudText>
                                </div>
                            </div>
                            <div class="d-flex align-center">
                                <MudIconButton Icon="@Icons.Material.Filled.Remove"
                                               Size="Size.Small"
                                               OnClick="() => DecreaseQuantity(item)" />
                                <MudText Typo="Typo.body1" Class="mx-3">@item.Quantity</MudText>
                                <MudIconButton Icon="@Icons.Material.Filled.Add"
                                               Size="Size.Small"
                                               OnClick="() => IncreaseQuantity(item)" />
                                <MudText Typo="Typo.h6" Class="ml-4" Color="Color.Primary">@item.TotalPrice.ToString("C")</MudText>
                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Size="Size.Small"
                                               Color="Color.Error"
                                               OnClick="() => RemoveFromCart(item)" />
                            </div>
                        </div>
                    }

                    <MudDivider Class="my-4" />

                    <div class="d-flex justify-space-between align-center mb-4">
                        <MudText Typo="Typo.h5">الإجمالي:</MudText>
                        <MudText Typo="Typo.h5" Color="Color.Primary">@cartItems.Sum(i => i.TotalPrice).ToString("C")</MudText>
                    </div>

                    <div class="d-flex justify-space-between">
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Secondary"
                                   OnClick="ClearCart">
                            <MudIcon Icon="@Icons.Material.Filled.Clear" Class="mr-2" />
                            إفراغ السلة
                        </MudButton>

                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Success"
                                   Class="btn-secondary-custom"
                                   OnClick="CreateOrderFromCart"
                                   Disabled="@(!cartItems.Any())">
                            <MudIcon Icon="@Icons.Material.Filled.ShoppingCartCheckout" Class="mr-2" />
                            إتمام الطلب
                        </MudButton>
                    </div>
                }
                else
                {
                    <div class="text-center pa-4">
                        <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Size="Size.Large" Class="mb-3 mud-text-secondary" />
                        <MudText Typo="Typo.h6" Class="mb-2 mud-text-secondary">سلة التسوق فارغة</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary mb-4">أضف منتجات إلى سلة التسوق لإنشاء طلب</MudText>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Href="/products">
                            <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="mr-2" />
                            تصفح المنتجات
                        </MudButton>
                    </div>
                }
            </MudCardContent>
        </MudCard>

        <!-- Quick Add Products -->
        <MudCard Class="custom-card mb-6 fade-in">
            <div class="custom-card-header">
                <MudText Typo="Typo.h5">إضافة سريعة للمنتجات</MudText>
            </div>
            <MudCardContent>
                <MudGrid>
                    @foreach (var product in availableProducts.Take(6))
                    {
                        <MudItem xs="12" sm="6" md="4" Class="mb-3">
                            <div class="quick-product-card d-flex align-center pa-2">
                                <img src="@(!string.IsNullOrEmpty(product.ImageUrl) ? product.ImageUrl : "/images/no-image.png")"
                                     alt="@product.Name"
                                     class="quick-product-image mr-3" />
                                <div class="flex-grow-1">
                                    <MudText Typo="Typo.body1" Class="font-weight-bold">@product.Name</MudText>
                                    <MudText Typo="Typo.body2" Color="Color.Primary">@product.Price.ToString("C")</MudText>
                                    <MudText Typo="Typo.caption" Class="mud-text-secondary">المخزون: @product.Stock</MudText>
                                </div>
                                <MudButton Variant="Variant.Filled"
                                           Size="Size.Small"
                                           Color="Color.Primary"
                                           OnClick="() => AddToCart(product)"
                                           Disabled="@(product.Stock <= 0)">
                                    <MudIcon Icon="@Icons.Material.Filled.Add" />
                                </MudButton>
                            </div>
                        </MudItem>
                    }
                </MudGrid>

                <div class="text-center mt-4">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               Href="/products">
                        <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="mr-2" />
                        عرض جميع المنتجات
                    </MudButton>
                </div>
            </MudCardContent>
        </MudCard>

        <!-- Orders List -->
        <MudCard Class="custom-card fade-in">
            <div class="custom-card-header">
                <div class="d-flex align-center justify-space-between">
                    <MudText Typo="Typo.h5">طلباتك</MudText>
                    <MudButton Variant="Variant.Text"
                               Color="Color.Primary"
                               OnClick="@(() => LoadCustomerOrders())">
                        <MudIcon Icon="@Icons.Material.Filled.Refresh" Class="mr-2" />
                        تحديث
                    </MudButton>
                </div>
            </div>
            <MudCardContent Class="pa-0">
                @if (orders.Any())
                {
                    @foreach (var order in orders)
                    {
                        <div class="order-item">
                            <div class="d-flex align-center justify-space-between pa-4">
                                <div class="d-flex align-center">
                                    <div class="order-icon @GetStatusClass(order.Status)">
                                        <MudIcon Icon="@GetStatusIcon(order.Status)" />
                                    </div>
                                    <div class="mr-4">
                                        <MudText Typo="Typo.h6" Class="mb-1">طلب رقم #@order.Id</MudText>
                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                            @order.Date.ToString("dd MMMM yyyy - HH:mm")
                                        </MudText>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <MudText Typo="Typo.h6" Color="Color.Primary">@order.Total.ToString("C")</MudText>
                                    <MudChip T="string" Size="Size.Small"
                                             Color="@GetStatusColor(order.Status)"
                                             Class="mt-1">
                                        @GetStatusText(order.Status)
                                    </MudChip>
                                </div>

                                <div class="d-flex align-center">
                                    @if (order.Status != "Cancelled" && order.Status != "Completed")
                                    {
                                        <MudButton Size="Size.Small"
                                                   Variant="Variant.Outlined"
                                                   Color="Color.Error"
                                                   OnClick="() => CancelOrder(order)"
                                                   Class="mr-2">
                                            <MudIcon Icon="@Icons.Material.Filled.Cancel" Size="Size.Small" Class="mr-1" />
                                            إلغاء
                                        </MudButton>
                                    }
                                    <MudButton Size="Size.Small"
                                               Variant="Variant.Text"
                                               Color="Color.Primary">
                                        <MudIcon Icon="@Icons.Material.Filled.Visibility" Size="Size.Small" Class="mr-1" />
                                        عرض
                                    </MudButton>
                                </div>
                            </div>
                            @if (order != orders.Last())
                            {
                                <MudDivider />
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="empty-state pa-8 text-center">
                        <MudIcon Icon="@Icons.Material.Filled.ShoppingCartCheckout" Size="Size.Large" Class="mb-4 mud-text-secondary" />
                        <MudText Typo="Typo.h6" Class="mb-2 mud-text-secondary">لا توجد طلبات حتى الآن</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            ابدأ بإنشاء طلبك الأول باستخدام النموذج أعلاه
                        </MudText>
                    </div>
                }
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private bool isCustomerLoggedIn = false;
    private CustomerEntity? currentCustomer;
    private List<OrderEntity> orders = new();
    private List<Product> availableProducts = new();
    private List<OrderItem> cartItems = new();

    // Order form fields
    private decimal orderTotal = 0;
    private string orderStatus = "Pending";

    protected override async Task OnInitializedAsync()
    {
        isCustomerLoggedIn = CustomerService.IsLoggedIn;
        currentCustomer = CustomerService.CurrentCustomer;

        if (isCustomerLoggedIn && currentCustomer != null)
        {
            await LoadCustomerOrders();
        }

        await LoadAvailableProducts();
        CustomerService.OnCustomerChanged += OnCustomerChanged;
    }

    public void Dispose()
    {
        CustomerService.OnCustomerChanged -= OnCustomerChanged;
    }

    private async void OnCustomerChanged()
    {
        isCustomerLoggedIn = CustomerService.IsLoggedIn;
        currentCustomer = CustomerService.CurrentCustomer;

        if (isCustomerLoggedIn && currentCustomer != null)
        {
            await LoadCustomerOrders();
        }
        else
        {
            orders.Clear();
        }

        StateHasChanged();
    }

    private async Task LoadCustomerOrders()
    {
        if (currentCustomer != null)
        {
            orders = await DbContext.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .Where(o => o.CustomerId == currentCustomer.Id)
                .OrderByDescending(o => o.Date)
                .ToListAsync();
        }
    }

    private async Task LoadAvailableProducts()
    {
        availableProducts = await DbContext.Products
            .Where(p => p.IsActive && p.Stock > 0)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    private void AddToCart(Product product)
    {
        var existingItem = cartItems.FirstOrDefault(ci => ci.ProductId == product.Id);

        if (existingItem != null)
        {
            existingItem.Quantity++;
        }
        else
        {
            var cartItem = new OrderItem
            {
                ProductId = product.Id,
                Product = product,
                Quantity = 1,
                UnitPrice = product.Price
            };
            cartItems.Add(cartItem);
        }

        StateHasChanged();
    }

    private void IncreaseQuantity(OrderItem item)
    {
        if (item.Quantity < item.Product.Stock)
        {
            item.Quantity++;
            StateHasChanged();
        }
    }

    private void DecreaseQuantity(OrderItem item)
    {
        if (item.Quantity > 1)
        {
            item.Quantity--;
        }
        else
        {
            cartItems.Remove(item);
        }
        StateHasChanged();
    }

    private void RemoveFromCart(OrderItem item)
    {
        cartItems.Remove(item);
        StateHasChanged();
    }

    private void ClearCart()
    {
        cartItems.Clear();
        StateHasChanged();
    }

    private async Task CreateOrderFromCart()
    {
        if (currentCustomer == null || !cartItems.Any())
        {
            await JSRuntime.InvokeVoidAsync("alert", "سلة التسوق فارغة!");
            return;
        }

        var newOrder = new OrderEntity
        {
            Date = DateTime.Now,
            Total = cartItems.Sum(ci => ci.TotalPrice),
            Status = "Pending",
            CustomerId = currentCustomer.Id,
            OrderItems = cartItems.ToList()
        };

        // Update product stock
        foreach (var item in cartItems)
        {
            var product = await DbContext.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                product.Stock -= item.Quantity;
                DbContext.Products.Update(product);
            }
        }

        DbContext.Orders.Add(newOrder);
        await DbContext.SaveChangesAsync();

        // Clear cart and refresh
        cartItems.Clear();
        await LoadCustomerOrders();
        await LoadAvailableProducts();

        await JSRuntime.InvokeVoidAsync("alert", "تم إنشاء الطلب بنجاح!");
        StateHasChanged();
    }

    private async Task CancelOrder(OrderEntity order)
    {
        order.CancelOrder();
        DbContext.Orders.Update(order);
        await DbContext.SaveChangesAsync();

        await JSRuntime.InvokeVoidAsync("alert", "Order cancelled successfully!");
        StateHasChanged();
    }

    private void Logout()
    {
        CustomerService.Logout();
        isCustomerLoggedIn = false;
        currentCustomer = null;
        orders.Clear();
        orderTotal = 0;
        orderStatus = "Pending";
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            "Pending" => "pending",
            "Processing" => "processing",
            "Completed" => "completed",
            "Cancelled" => "cancelled",
            _ => "pending"
        };
    }

    private string GetStatusIcon(string status)
    {
        return status switch
        {
            "Pending" => Icons.Material.Filled.Schedule,
            "Processing" => Icons.Material.Filled.Autorenew,
            "Completed" => Icons.Material.Filled.CheckCircle,
            "Cancelled" => Icons.Material.Filled.Cancel,
            _ => Icons.Material.Filled.Schedule
        };
    }

    private Color GetStatusColor(string status)
    {
        return status switch
        {
            "Pending" => Color.Warning,
            "Processing" => Color.Info,
            "Completed" => Color.Success,
            "Cancelled" => Color.Error,
            _ => Color.Default
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Pending" => "قيد الانتظار",
            "Processing" => "قيد المعالجة",
            "Completed" => "مكتمل",
            "Cancelled" => "ملغي",
            _ => "غير محدد"
        };
    }
}
