@page "/order"
@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using OnlineStore.Services
@using Microsoft.EntityFrameworkCore
@using OrderEntity = OnlineStore.Data.Entities.Order
@using CustomerEntity = OnlineStore.Data.Entities.Customer
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject CustomerService CustomerService
@rendermode InteractiveServer

<PageTitle>Order</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">Order Management</MudText>

    @if (!isCustomerLoggedIn)
    {
        <!-- Customer Login Required Section -->
        <MudCard Class="mb-4">
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Login Required</MudText>
                <MudText Typo="Typo.body1" Class="mb-3">You need to login as a customer to access orders.</MudText>

                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           Href="/customer">
                    Go to Customer Login
                </MudButton>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <!-- Order Creation Section -->
        <MudCard Class="mb-4">
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Welcome, @currentCustomer?.Name!</MudText>
                
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="orderTotal" 
                                      Label="Order Total" 
                                      Variant="Variant.Outlined" 
                                      Required="true"
                                      Format="F2" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="orderStatus" 
                                   Label="Status" 
                                   Variant="Variant.Outlined">
                            <MudSelectItem Value="@("Pending")">Pending</MudSelectItem>
                            <MudSelectItem Value="@("Processing")">Processing</MudSelectItem>
                            <MudSelectItem Value="@("Completed")">Completed</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                </MudGrid>

                <MudButton Variant="Variant.Filled" 
                           Color="Color.Success" 
                           Class="mt-3 mr-2"
                           OnClick="CreateOrder">
                    Create Order
                </MudButton>

                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           Class="mt-3"
                           OnClick="Logout">
                    Logout
                </MudButton>
            </MudCardContent>
        </MudCard>

        <!-- Orders List -->
        <MudCard>
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Your Orders</MudText>
                
                <MudTable Items="@orders" Hover="true" Striped="true">
                    <HeaderContent>
                        <MudTh>Order ID</MudTh>
                        <MudTh>Date</MudTh>
                        <MudTh>Total</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Actions</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Order ID">@context.Id</MudTd>
                        <MudTd DataLabel="Date">@context.Date.ToString("dd/MM/yyyy")</MudTd>
                        <MudTd DataLabel="Total">@context.Total.ToString("C")</MudTd>
                        <MudTd DataLabel="Status">@context.Status</MudTd>
                        <MudTd DataLabel="Actions">
                            @if (context.Status != "Cancelled")
                            {
                                <MudButton Size="Size.Small" 
                                           Variant="Variant.Filled" 
                                           Color="Color.Error"
                                           OnClick="() => CancelOrder(context)">
                                    Cancel
                                </MudButton>
                            }
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private bool isCustomerLoggedIn = false;
    private CustomerEntity? currentCustomer;
    private List<OrderEntity> orders = new();

    // Order form fields
    private float orderTotal = 0;
    private string orderStatus = "Pending";

    protected override async Task OnInitializedAsync()
    {
        isCustomerLoggedIn = CustomerService.IsLoggedIn;
        currentCustomer = CustomerService.CurrentCustomer;

        if (isCustomerLoggedIn && currentCustomer != null)
        {
            await LoadCustomerOrders();
        }

        CustomerService.OnCustomerChanged += OnCustomerChanged;
    }

    public void Dispose()
    {
        CustomerService.OnCustomerChanged -= OnCustomerChanged;
    }

    private async void OnCustomerChanged()
    {
        isCustomerLoggedIn = CustomerService.IsLoggedIn;
        currentCustomer = CustomerService.CurrentCustomer;

        if (isCustomerLoggedIn && currentCustomer != null)
        {
            await LoadCustomerOrders();
        }
        else
        {
            orders.Clear();
        }

        StateHasChanged();
    }

    private async Task LoadCustomerOrders()
    {
        if (currentCustomer != null)
        {
            orders = await DbContext.Orders
                .Where(o => o.CustomerId == currentCustomer.Id)
                .OrderByDescending(o => o.Date)
                .ToListAsync();
        }
    }

    private async Task CreateOrder()
    {
        if (currentCustomer == null || orderTotal <= 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Please enter a valid order total.");
            return;
        }

        var newOrder = new OrderEntity
        {
            Date = DateTime.Now,
            Total = orderTotal,
            Status = orderStatus,
            CustomerId = currentCustomer.Id
        };

        DbContext.Orders.Add(newOrder);
        await DbContext.SaveChangesAsync();

        // Refresh orders list
        await LoadCustomerOrders();

        // Reset form
        orderTotal = 0;
        orderStatus = "Pending";

        await JSRuntime.InvokeVoidAsync("alert", "Order created successfully!");
        StateHasChanged();
    }

    private async Task CancelOrder(OrderEntity order)
    {
        order.CancelOrder();
        DbContext.Orders.Update(order);
        await DbContext.SaveChangesAsync();

        await JSRuntime.InvokeVoidAsync("alert", "Order cancelled successfully!");
        StateHasChanged();
    }

    private void Logout()
    {
        CustomerService.Logout();
        isCustomerLoggedIn = false;
        currentCustomer = null;
        orders.Clear();
        orderTotal = 0;
        orderStatus = "Pending";
    }
}
