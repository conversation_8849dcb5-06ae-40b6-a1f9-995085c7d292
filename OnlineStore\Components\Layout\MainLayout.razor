﻿@inherits LayoutComponentBase
<MudRTLProvider RightToLeft="true">
    <MudThemeProvider Theme="@customTheme" />
    <MudPopoverProvider />
    <MudDialogProvider />
    <MudSnackbarProvider />

    <MudLayout>
        <MudAppBar Elevation="3" Class="custom-appbar">
            <MudStaticNavDrawerToggle DrawerId="nav-drawer" Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" />
            <MudIcon Icon="@Icons.Material.Filled.Store" Class="mr-3" />
            <MudText Typo="Typo.h5" Class="ml-3">متجر إلكتروني</MudText>
            <MudSpacer />
            <MudIconButton Icon="@Icons.Material.Filled.Notifications" Color="Color.Inherit" />
            <MudIconButton Icon="@Icons.Material.Filled.AccountCircle" Color="Color.Inherit" />
        </MudAppBar>
        <MudDrawer id="nav-drawer" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="3" Class="custom-drawer">
            <div class="drawer-header">
                <MudIcon Icon="@Icons.Material.Filled.Store" Size="Size.Large" Class="mb-2" />
                <MudText Typo="Typo.h6">متجر إلكتروني</MudText>
                <MudText Typo="Typo.caption" Class="mud-text-secondary">نظام إدارة شامل</MudText>
            </div>
            <NavMenu />
        </MudDrawer>
        <MudMainContent Class="pt-16 pa-4 custom-main-content">
            <div class="fade-in">
                @Body
            </div>
        </MudMainContent>
    </MudLayout>
</MudRTLProvider>


<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    private bool _drawerOpen = true;

    private readonly MudTheme customTheme = new()
    {
        PaletteLight = new PaletteLight()
        {
            Primary = "#2563eb",
            Secondary = "#10b981",
            Tertiary = "#f59e0b",
            Info = "#3b82f6",
            Success = "#10b981",
            Warning = "#f59e0b",
            Error = "#ef4444",
            Dark = "#1f2937",
            Background = "#f8fafc",
            Surface = "#ffffff",
            AppbarBackground = "#2563eb",
            DrawerBackground = "#ffffff",
            TextPrimary = "#1f2937",
            TextSecondary = "#6b7280"
        },
        LayoutProperties = new LayoutProperties()
        {
            DrawerWidthLeft = "280px",
            AppbarHeight = "64px"
        },
        Typography = new Typography()
        {
            Default = new Default()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "0.875rem",
                FontWeight = 400,
                LineHeight = 1.6
            },
            H1 = new H1()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "2.5rem",
                FontWeight = 700,
                LineHeight = 1.2
            },
            H2 = new H2()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "2rem",
                FontWeight = 600,
                LineHeight = 1.3
            },
            H3 = new H3()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "1.75rem",
                FontWeight = 600,
                LineHeight = 1.3
            },
            H4 = new H4()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "1.5rem",
                FontWeight = 600,
                LineHeight = 1.4
            },
            H5 = new H5()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "1.25rem",
                FontWeight = 600,
                LineHeight = 1.4
            },
            H6 = new H6()
            {
                FontFamily = new[] { "Inter", "Segoe UI", "Roboto", "sans-serif" },
                FontSize = "1.125rem",
                FontWeight = 600,
                LineHeight = 1.4
            }
        }
    };
}


