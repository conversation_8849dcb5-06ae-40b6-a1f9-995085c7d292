@page "/customer"
@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using OnlineStore.Services
@using OnlineStore.Components.Pages.Dialogs
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject IDialogService DialogService
@inject CustomerService CustomerService
@rendermode InteractiveServer

<PageTitle>Customer</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">Customer Management</MudText>

    @if (!isCustomerLoggedIn)
    {
        <!-- Welcome Section -->
        <MudCard Class="mb-4">
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Welcome to Customer Portal</MudText>
                <MudText Typo="Typo.body1" Class="mb-4">Please login to access your account or register for a new account.</MudText>
                
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           Class="mr-3"
                           OnClick="OpenLoginDialog">
                    Login
                </MudButton>

                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary"
                           OnClick="OpenRegisterDialog">
                    Register
                </MudButton>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <!-- Customer Dashboard -->
        <MudCard Class="mb-4">
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Welcome, @currentCustomer?.Name!</MudText>
                <MudText Typo="Typo.body1" Class="mb-3">Email: @currentCustomer?.Email</MudText>
                
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           Class="mr-3"
                           Href="/order">
                    Go to Orders
                </MudButton>

                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary"
                           OnClick="Logout">
                    Logout
                </MudButton>
            </MudCardContent>
        </MudCard>

        <!-- Customer Info -->
        <MudCard>
            <MudCardContent>
                <MudText Typo="Typo.h6" Class="mb-3">Account Information</MudText>
                
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField Value="@currentCustomer?.Name" 
                                      Label="Name" 
                                      Variant="Variant.Outlined" 
                                      ReadOnly="true" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudTextField Value="@currentCustomer?.Email" 
                                      Label="Email" 
                                      Variant="Variant.Outlined" 
                                      ReadOnly="true" />
                    </MudItem>
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private bool isCustomerLoggedIn = false;
    private OnlineStore.Data.Entities.Customer? currentCustomer;

    protected override void OnInitialized()
    {
        isCustomerLoggedIn = CustomerService.IsLoggedIn;
        currentCustomer = CustomerService.CurrentCustomer;
        CustomerService.OnCustomerChanged += StateHasChanged;
    }

    public void Dispose()
    {
        CustomerService.OnCustomerChanged -= StateHasChanged;
    }

    private async Task OpenLoginDialog()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Small, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<LoginDialog>("Customer Login", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is OnlineStore.Data.Entities.Customer customer)
        {
            CustomerService.SetCurrentCustomer(customer);
            currentCustomer = customer;
            isCustomerLoggedIn = true;
        }
    }

    private async Task OpenRegisterDialog()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Small, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<RegisterDialog>("Customer Registration", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is OnlineStore.Data.Entities.Customer customer)
        {
            CustomerService.SetCurrentCustomer(customer);
            currentCustomer = customer;
            isCustomerLoggedIn = true;
        }
    }

    private void Logout()
    {
        CustomerService.Logout();
        isCustomerLoggedIn = false;
        currentCustomer = null;
    }

    // Static method to get current logged in customer (for use in other pages)
    public static OnlineStore.Data.Entities.Customer? GetCurrentCustomer()
    {
        // This is a simple implementation. In a real app, you'd use proper session management
        return _currentCustomer;
    }

    private static OnlineStore.Data.Entities.Customer? _currentCustomer;

    private void SetCurrentCustomer(OnlineStore.Data.Entities.Customer? customer)
    {
        _currentCustomer = customer;
    }
}
