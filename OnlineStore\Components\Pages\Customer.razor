@page "/customer"
@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using OnlineStore.Services
@using OnlineStore.Components.Pages.Dialogs
@using Microsoft.EntityFrameworkCore
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject IDialogService DialogService
@inject CustomerService CustomerService
@rendermode InteractiveServer

<PageTitle>العملاء - متجر إلكتروني</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    @if (!isCustomerLoggedIn)
    {
        <!-- Hero Section for Customers -->
        <div class="hero-section slide-up mb-6">
            <div class="hero-content">
                <MudIcon Icon="@Icons.Material.Filled.People" Size="Size.Large" Class="mb-3" />
                <MudText Typo="Typo.h3" Class="hero-title">مرحباً بك في بوابة العملاء</MudText>
                <MudText Typo="Typo.h6" Class="hero-subtitle">
                    سجل دخولك للوصول إلى حسابك أو أنشئ حساباً جديداً للاستمتاع بخدماتنا المميزة
                </MudText>
            </div>
        </div>

        <!-- Login/Register Cards -->
        <MudGrid>
            <MudItem xs="12" md="6" Class="fade-in">
                <MudCard Class="custom-card h-100">
                    <div class="custom-card-header text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Login" Size="Size.Large" Class="mb-2" />
                        <MudText Typo="Typo.h5">تسجيل الدخول</MudText>
                    </div>
                    <MudCardContent Class="custom-card-content text-center">
                        <MudText Typo="Typo.body1" Class="mb-4 mud-text-secondary">
                            لديك حساب بالفعل؟ سجل دخولك للوصول إلى طلباتك ومعلوماتك الشخصية
                        </MudText>

                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Size="Size.Large"
                                   Class="btn-primary-custom"
                                   FullWidth="true"
                                   OnClick="OpenLoginDialog">
                            <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" />
                            تسجيل الدخول
                        </MudButton>

                        <MudText Typo="Typo.caption" Class="mt-3 mud-text-secondary">
                            آمن ومحمي بأعلى معايير الأمان
                        </MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" md="6" Class="fade-in">
                <MudCard Class="custom-card h-100">
                    <div class="custom-card-header text-center" style="background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));">
                        <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Size="Size.Large" Class="mb-2" />
                        <MudText Typo="Typo.h5">إنشاء حساب جديد</MudText>
                    </div>
                    <MudCardContent Class="custom-card-content text-center">
                        <MudText Typo="Typo.body1" Class="mb-4 mud-text-secondary">
                            عميل جديد؟ أنشئ حسابك الآن واستمتع بمزايا حصرية وعروض خاصة
                        </MudText>

                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Secondary"
                                   Size="Size.Large"
                                   Class="btn-secondary-custom"
                                   FullWidth="true"
                                   OnClick="OpenRegisterDialog">
                            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="mr-2" />
                            إنشاء حساب جديد
                        </MudButton>

                        <MudText Typo="Typo.caption" Class="mt-3 mud-text-secondary">
                            مجاني تماماً ولا يستغرق سوى دقائق
                        </MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Benefits Section -->
        <MudContainer MaxWidth="MaxWidth.Large" Class="mt-8">
            <MudText Typo="Typo.h5" Align="Align.Center" Class="mb-6" Color="Color.Primary">
                مزايا إنشاء حساب معنا
            </MudText>

            <MudGrid>
                <MudItem xs="12" md="4" Class="fade-in">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <MudIcon Icon="@Icons.Material.Filled.History" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">تتبع الطلبات</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            تابع حالة طلباتك لحظة بلحظة من الطلب حتى التسليم
                        </MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" md="4" Class="fade-in">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <MudIcon Icon="@Icons.Material.Filled.Favorite" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">قائمة المفضلة</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            احفظ منتجاتك المفضلة واحصل على تنبيهات العروض الخاصة
                        </MudText>
                    </div>
                </MudItem>

                <MudItem xs="12" md="4" Class="fade-in">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <MudIcon Icon="@Icons.Material.Filled.LocalOffer" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">عروض حصرية</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                            احصل على خصومات وعروض حصرية للأعضاء المسجلين فقط
                        </MudText>
                    </div>
                </MudItem>
            </MudGrid>
        </MudContainer>
    }
    else
    {
        <!-- Customer Dashboard Header -->
        <div class="customer-dashboard-header slide-up mb-6">
            <MudCard Class="custom-card">
                <div class="custom-card-header">
                    <div class="d-flex align-center">
                        <MudAvatar Color="Color.Surface" Size="Size.Large" Class="mr-4">
                            @currentCustomer?.Name?.Substring(0, 1).ToUpper()
                        </MudAvatar>
                        <div>
                            <MudText Typo="Typo.h4" Class="mb-1">مرحباً، @currentCustomer?.Name!</MudText>
                            <MudText Typo="Typo.body1" Class="opacity-90">@currentCustomer?.Email</MudText>
                        </div>
                        <MudSpacer />
                        <MudChip T="string" Color="Color.Success" Size="Size.Medium">
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="mr-1" />
                            عضو نشط
                        </MudChip>
                    </div>
                </div>
            </MudCard>
        </div>

        <!-- Quick Actions -->
        <MudGrid Class="mb-6">
            <MudItem xs="12" md="6" Class="fade-in">
                <MudCard Class="custom-card action-card" Style="cursor: pointer;" @onclick="@(() => NavigateToOrders())">
                    <MudCardContent Class="text-center pa-6">
                        <div class="action-icon mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Size="Size.Large" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">إدارة الطلبات</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary mb-4">
                            عرض وتتبع جميع طلباتك السابقة والحالية
                        </MudText>
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Class="btn-primary-custom"
                                   Href="/order">
                            <MudIcon Icon="@Icons.Material.Filled.ArrowForward" Class="mr-2" />
                            عرض الطلبات
                        </MudButton>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12" md="6" Class="fade-in">
                <MudCard Class="custom-card action-card" Style="cursor: pointer;">
                    <MudCardContent Class="text-center pa-6">
                        <div class="action-icon mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Large" />
                        </div>
                        <MudText Typo="Typo.h6" Class="mb-2">الملف الشخصي</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary mb-4">
                            تحديث معلوماتك الشخصية وإعدادات الحساب
                        </MudText>
                        <MudButton Variant="Variant.Outlined"
                                   Color="Color.Primary"
                                   Class="btn-outline-custom">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                            تحديث الملف
                        </MudButton>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Account Information -->
        <MudCard Class="custom-card fade-in">
            <div class="custom-card-header">
                <MudText Typo="Typo.h5">معلومات الحساب</MudText>
            </div>
            <MudCardContent Class="custom-card-content">
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField Value="@currentCustomer?.Name"
                                      Label="الاسم الكامل"
                                      Variant="Variant.Outlined"
                                      ReadOnly="true"
                                      Class="mb-4" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudTextField Value="@currentCustomer?.Email"
                                      Label="البريد الإلكتروني"
                                      Variant="Variant.Outlined"
                                      ReadOnly="true"
                                      Class="mb-4" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudTextField Value="@joinDate"
                                      Label="تاريخ الانضمام"
                                      Variant="Variant.Outlined"
                                      ReadOnly="true"
                                      Class="mb-4" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudTextField Value="@accountStatus"
                                      Label="حالة الحساب"
                                      Variant="Variant.Outlined"
                                      ReadOnly="true"
                                      Class="mb-4" />
                    </MudItem>
                </MudGrid>

                <MudDivider Class="my-4" />

                <div class="d-flex justify-end">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Error"
                               OnClick="Logout"
                               Class="logout-btn">
                        <MudIcon Icon="@Icons.Material.Filled.Logout" Class="mr-2" />
                        تسجيل الخروج
                    </MudButton>
                </div>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private bool isCustomerLoggedIn = false;
    private OnlineStore.Data.Entities.Customer? currentCustomer;
    private string joinDate = DateTime.Now.ToString("dd/MM/yyyy");
    private string accountStatus = "عضو نشط";

    protected override void OnInitialized()
    {
        isCustomerLoggedIn = CustomerService.IsLoggedIn;
        currentCustomer = CustomerService.CurrentCustomer;
        CustomerService.OnCustomerChanged += StateHasChanged;
    }

    public void Dispose()
    {
        CustomerService.OnCustomerChanged -= StateHasChanged;
    }

    private async Task OpenLoginDialog()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Small, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<LoginDialog>("Customer Login", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is OnlineStore.Data.Entities.Customer customer)
        {
            CustomerService.SetCurrentCustomer(customer);
            currentCustomer = customer;
            isCustomerLoggedIn = true;
        }
    }

    private async Task OpenRegisterDialog()
    {
        var parameters = new DialogParameters();
        var options = new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Small, FullWidth = true };
        
        var dialog = await DialogService.ShowAsync<RegisterDialog>("Customer Registration", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is OnlineStore.Data.Entities.Customer customer)
        {
            CustomerService.SetCurrentCustomer(customer);
            currentCustomer = customer;
            isCustomerLoggedIn = true;
        }
    }

    private void Logout()
    {
        CustomerService.Logout();
        isCustomerLoggedIn = false;
        currentCustomer = null;
    }

    private void NavigateToOrders()
    {
        // Navigation will be handled by the href attribute in the button
    }

    // Static method to get current logged in customer (for use in other pages)
    public static OnlineStore.Data.Entities.Customer? GetCurrentCustomer()
    {
        // This is a simple implementation. In a real app, you'd use proper session management
        return _currentCustomer;
    }

    private static OnlineStore.Data.Entities.Customer? _currentCustomer;

    private void SetCurrentCustomer(OnlineStore.Data.Entities.Customer? customer)
    {
        _currentCustomer = customer;
    }
}
