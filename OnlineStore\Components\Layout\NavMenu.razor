@implements IDisposable

@inject NavigationManager NavigationManager

<MudNavMenu Class="custom-nav-menu">
    <!-- Main Navigation -->
    <MudText Typo="Typo.caption" Class="nav-section-title">التنقل الرئيسي</MudText>
    <MudNavLink Href="" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Dashboard" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-3" />
        لوحة التحكم
    </MudNavLink>

    <!-- Store Management -->
    <MudText Typo="Typo.caption" Class="nav-section-title mt-4">إدارة المتجر</MudText>
    <MudNavLink Href="customer" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.People" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.People" Class="mr-3" />
        العملاء
        <MudChip T="string" Size="Size.Small" Color="Color.Primary" Class="mr-auto">جديد</MudChip>
    </MudNavLink>
    <MudNavLink Href="order" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.ShoppingCart" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Class="mr-3" />
        الطلبات
    </MudNavLink>
    <MudNavLink Href="administrationList" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Business" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.Business" Class="mr-3" />
        الإدارات
    </MudNavLink>

    <!-- Tools & Utilities -->
    <MudText Typo="Typo.caption" Class="nav-section-title mt-4">الأدوات</MudText>
    <MudNavLink Href="counter" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Calculate" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.Calculate" Class="mr-3" />
        العداد
    </MudNavLink>
    <MudNavLink Href="weather" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.WbSunny" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.WbSunny" Class="mr-3" />
        حالة الطقس
    </MudNavLink>

    <!-- Security -->
    <MudText Typo="Typo.caption" Class="nav-section-title mt-4">الأمان</MudText>
    <MudNavLink Href="auth" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Security" Class="nav-link-custom">
        <MudIcon Icon="@Icons.Material.Filled.Security" Class="mr-3" />
        المصادقة المطلوبة
    </MudNavLink>
    <!-- User Account Section -->
    <MudDivider Class="my-4" />
    <AuthorizeView>
        <Authorized>
            <div class="user-profile-section">
                <MudCard Class="user-profile-card">
                    <MudCardContent Class="pa-3">
                        <div class="d-flex align-center">
                            <MudAvatar Color="Color.Primary" Size="Size.Medium" Class="mr-3">
                                @context.User.Identity?.Name?.Substring(0, 1).ToUpper()
                            </MudAvatar>
                            <div>
                                <MudText Typo="Typo.body2" Class="font-weight-bold">@context.User.Identity?.Name</MudText>
                                <MudText Typo="Typo.caption" Class="mud-text-secondary">مستخدم مسجل</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </div>
            <MudNavLink Href="Account/Manage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.ManageAccounts" Class="nav-link-custom">
                <MudIcon Icon="@Icons.Material.Filled.ManageAccounts" Class="mr-3" />
                إدارة الحساب
            </MudNavLink>
            <form action="Account/Logout" method="post" class="logout-form">
                <AntiforgeryToken />
                <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                <button type="submit" class="logout-button">
                    <MudIcon Icon="@Icons.Material.Filled.Logout" Class="mr-3" />
                    تسجيل الخروج
                </button>
            </form>
        </Authorized>
        <NotAuthorized>
            <div class="auth-section">
                <MudText Typo="Typo.caption" Class="nav-section-title">الحساب</MudText>
                <MudNavLink Href="Account/Register" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.PersonAdd" Class="nav-link-custom">
                    <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="mr-3" />
                    إنشاء حساب
                </MudNavLink>
                <MudNavLink Href="Account/Login" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Login" Class="nav-link-custom">
                    <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-3" />
                    تسجيل الدخول
                </MudNavLink>
            </div>
        </NotAuthorized>
    </AuthorizeView>
</MudNavMenu>


@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

