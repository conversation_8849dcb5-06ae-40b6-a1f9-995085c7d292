﻿@implements IDisposable

@inject NavigationManager NavigationManager

<MudNavMenu Class="custom-nav-menu compact-nav">
    <!-- Main Navigation -->
    <MudNavLink Href="" Match="NavLinkMatch.All" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" Size="Size.Small" />
        لوحة التحكم
    </MudNavLink>

    <!-- Store Management -->
    <MudNavLink Href="products" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="mr-2" Size="Size.Small" />
        المنتجات
        <MudChip T="string" Size="Size.Small" Color="Color.Secondary" Class="mr-auto compact-chip">جديد</MudChip>
    </MudNavLink>
    <MudNavLink Href="customer" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.People" Class="mr-2" Size="Size.Small" />
        العملاء
    </MudNavLink>
    <MudNavLink Href="order" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Class="mr-2" Size="Size.Small" />
        الطلبات
    </MudNavLink>
    <MudNavLink Href="administrationList" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.Business" Class="mr-2" Size="Size.Small" />
        الإدارات
    </MudNavLink>

    <!-- Tools & Utilities -->
    <MudNavLink Href="counter" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.Calculate" Class="mr-2" Size="Size.Small" />
        العداد
    </MudNavLink>
    <MudNavLink Href="weather" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
        <MudIcon Icon="@Icons.Material.Filled.WbSunny" Class="mr-2" Size="Size.Small" />
        حالة الطقس
    </MudNavLink>
    <!-- User Account Section -->
    <MudDivider Class="my-2" />
    <AuthorizeView>
        <Authorized>
            <MudNavLink Href="Account/Manage" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
                <MudIcon Icon="@Icons.Material.Filled.ManageAccounts" Class="mr-2" Size="Size.Small" />
                إدارة الحساب
            </MudNavLink>
            <form action="Account/Logout" method="post" class="logout-form-compact">
                <AntiforgeryToken />
                <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                <button type="submit" class="logout-button-compact">
                    <MudIcon Icon="@Icons.Material.Filled.Logout" Class="mr-2" Size="Size.Small" />
                    تسجيل الخروج
                </button>
            </form>
        </Authorized>
        <NotAuthorized>
            <MudNavLink Href="Account/Register" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
                <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="mr-2" Size="Size.Small" />
                إنشاء حساب
            </MudNavLink>
            <MudNavLink Href="Account/Login" Match="NavLinkMatch.Prefix" Class="nav-link-compact">
                <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" Size="Size.Small" />
                تسجيل الدخول
            </MudNavLink>
        </NotAuthorized>
    </AuthorizeView>
</MudNavMenu>


@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

