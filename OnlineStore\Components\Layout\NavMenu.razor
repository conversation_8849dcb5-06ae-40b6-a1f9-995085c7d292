﻿@implements IDisposable

@inject NavigationManager NavigationManager

<MudNavMenu>
    <MudNavLink Href="" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">الرئيسية</MudNavLink>
    <MudNavLink Href="counter" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Add">عداد</MudNavLink>
    
    <MudNavLink Href="weather" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.List">طقس</MudNavLink>
    <MudNavLink Href="administrationList" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.List">الإدارات</MudNavLink>
    <MudNavLink Href="customer" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Person">العملاء</MudNavLink>
    <MudNavLink Href="order" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.ShoppingCart">الطلبات</MudNavLink>

    <MudNavLink Href="auth" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Lock">مطلوب المصادقة</MudNavLink>
    <AuthorizeView>
        <Authorized>
            <MudNavLink Href="Account/Manage" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Person">@context.User.Identity?.Name</MudNavLink>
            <form action="Account/Logout" method="post">
                <AntiforgeryToken />
                <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                <button type="submit" class="mud-nav-link mud-ripple">
                    <MudIcon Icon="@Icons.Material.Filled.Logout" Color="Color.Info" Class="mr-3"></MudIcon> خروج
                </button>
            </form>
        </Authorized>
        <NotAuthorized>
            <MudNavLink Href="Account/Register" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Person">تسجيل</MudNavLink>
            <MudNavLink Href="Account/Login" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Password">دخول</MudNavLink>
        </NotAuthorized>
    </AuthorizeView>
</MudNavMenu>


@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

