namespace OnlineStore.Data.Entities
{
    public class Order
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public float Total { get; set; }
        public string Status { get; set; } = string.Empty;
        public int CustomerId { get; set; }
        public Customer Customer { get; set; } = default!;

        public void CancelOrder()
        {
            Status = "Cancelled";
        }
    }
}
