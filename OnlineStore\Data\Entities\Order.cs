using System.ComponentModel.DataAnnotations;

namespace OnlineStore.Data.Entities
{
    public class Order
    {
        public int Id { get; set; }

        public DateTime Date { get; set; } = DateTime.Now;

        public decimal Total { get; set; }

        [StringLength(50)]
        public string Status { get; set; } = "Pending";

        [Required]
        public int CustomerId { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(200)]
        public string? ShippingAddress { get; set; }

        public DateTime? ShippedDate { get; set; }

        public DateTime? DeliveredDate { get; set; }

        // Navigation properties
        public virtual Customer Customer { get; set; } = default!;
        public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

        // Calculated properties
        public decimal CalculatedTotal => OrderItems?.Sum(oi => oi.TotalPrice) ?? 0;

        public int TotalItems => OrderItems?.Sum(oi => oi.Quantity) ?? 0;

        public void CancelOrder()
        {
            if (Status == "Pending" || Status == "Processing")
            {
                Status = "Cancelled";
            }
        }

        public void CompleteOrder()
        {
            Status = "Completed";
            DeliveredDate = DateTime.Now;
        }

        public void ShipOrder()
        {
            Status = "Shipped";
            ShippedDate = DateTime.Now;
        }
    }
}
