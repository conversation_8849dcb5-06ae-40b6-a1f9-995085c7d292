@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using Microsoft.EntityFrameworkCore
@using MudBlazor
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudText Typo="Typo.h6" Class="mb-4">إضافة منتج جديد</MudText>
            
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="product.Name" 
                                  Label="اسم المنتج" 
                                  Variant="Variant.Outlined" 
                                  Required="true"
                                  Class="mb-3" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="product.Description" 
                                  Label="وصف المنتج" 
                                  Variant="Variant.Outlined" 
                                  Lines="3"
                                  Class="mb-3" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="product.Price" 
                                     Label="السعر" 
                                     Variant="Variant.Outlined" 
                                     Required="true"
                                     Min="0.01m"
                                     Format="F2"
                                     Class="mb-3" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="product.Stock" 
                                     Label="الكمية المتوفرة" 
                                     Variant="Variant.Outlined" 
                                     Min="0"
                                     Class="mb-3" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="product.Category" 
                               Label="الفئة" 
                               Variant="Variant.Outlined"
                               Class="mb-3">
                        <MudSelectItem Value="@("إلكترونيات")">إلكترونيات</MudSelectItem>
                        <MudSelectItem Value="@("كتب")">كتب</MudSelectItem>
                        <MudSelectItem Value="@("ملابس")">ملابس</MudSelectItem>
                        <MudSelectItem Value="@("أدوات منزلية")">أدوات منزلية</MudSelectItem>
                        <MudSelectItem Value="@("رياضة")">رياضة</MudSelectItem>
                        <MudSelectItem Value="@("أخرى")">أخرى</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="product.ImageUrl" 
                                  Label="رابط الصورة" 
                                  Variant="Variant.Outlined" 
                                  Class="mb-3" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudSwitch @bind-Value="product.IsActive" 
                               Label="المنتج نشط" 
                               Color="Color.Primary" />
                </MudItem>
            </MudGrid>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <MudAlert Severity="Severity.Error" Class="mb-3">@errorMessage</MudAlert>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">إلغاء</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveProduct">حفظ</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = default!;

    private Product product = new Product();
    private string errorMessage = string.Empty;

    private async Task SaveProduct()
    {
        errorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(product.Name))
        {
            errorMessage = "يرجى إدخال اسم المنتج.";
            return;
        }

        if (product.Price <= 0)
        {
            errorMessage = "يرجى إدخال سعر صحيح للمنتج.";
            return;
        }

        try
        {
            product.CreatedDate = DateTime.Now;
            
            DbContext.Products.Add(product);
            await DbContext.SaveChangesAsync();

            MudDialog.Close(DialogResult.Ok(product));
        }
        catch (Exception ex)
        {
            errorMessage = "حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.";
            await JSRuntime.InvokeVoidAsync("console.error", ex.Message);
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
