@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using Microsoft.EntityFrameworkCore
@using MudBlazor
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 400px;">
            <MudText Typo="Typo.h6" Class="mb-4">Customer Registration</MudText>
            
            <MudTextField @bind-Value="name" 
                          Label="Full Name" 
                          Variant="Variant.Outlined" 
                          Required="true"
                          Class="mb-3" />
            
            <MudTextField @bind-Value="email" 
                          Label="Email" 
                          Variant="Variant.Outlined" 
                          Required="true"
                          Class="mb-3" />
            
            <MudTextField @bind-Value="password" 
                          Label="Password" 
                          Variant="Variant.Outlined" 
                          InputType="InputType.Password"
                          Required="true"
                          Class="mb-3" />

            <MudTextField @bind-Value="confirmPassword" 
                          Label="Confirm Password" 
                          Variant="Variant.Outlined" 
                          InputType="InputType.Password"
                          Required="true"
                          Class="mb-3" />

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <MudAlert Severity="Severity.Error" Class="mb-3">@errorMessage</MudAlert>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Cancel</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="Register">Register</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] IMudDialogInstance MudDialog { get; set; } = default!;

    private string name = string.Empty;
    private string email = string.Empty;
    private string password = string.Empty;
    private string confirmPassword = string.Empty;
    private string errorMessage = string.Empty;

    private async Task Register()
    {
        errorMessage = string.Empty;

        if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(email) || 
            string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(confirmPassword))
        {
            errorMessage = "Please fill in all fields.";
            return;
        }

        if (password != confirmPassword)
        {
            errorMessage = "Passwords do not match.";
            return;
        }

        if (password.Length < 6)
        {
            errorMessage = "Password must be at least 6 characters long.";
            return;
        }

        try
        {
            // Check if email already exists
            var existingCustomer = await DbContext.Customers
                .FirstOrDefaultAsync(c => c.Email == email);

            if (existingCustomer != null)
            {
                errorMessage = "Email already exists. Please use a different email.";
                return;
            }

            // Create new customer
            var newCustomer = new OnlineStore.Data.Entities.Customer
            {
                Name = name,
                Email = email,
                Password = password // In a real app, you should hash the password
            };

            DbContext.Customers.Add(newCustomer);
            await DbContext.SaveChangesAsync();

            MudDialog.Close(DialogResult.Ok(newCustomer));
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during registration. Please try again.";
            await JSRuntime.InvokeVoidAsync("console.error", ex.Message);
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
