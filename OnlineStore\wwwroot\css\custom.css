/* Custom Theme for Online Store */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #10b981;
    --secondary-dark: #059669;
    --accent-color: #f59e0b;
    --accent-dark: #d97706;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Custom Card Styles */
.custom-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    overflow: hidden;
}

.custom-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.custom-card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1.5rem;
    border-bottom: none;
}

.custom-card-content {
    padding: 2rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 4rem 2rem;
    text-align: center;
    border-radius: var(--border-radius-lg);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Feature Cards */
.feature-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
}

.feature-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

/* Custom Buttons */
.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: white;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary-custom {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: white;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.btn-secondary-custom:hover {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* Navigation Improvements */
.mud-appbar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    box-shadow: var(--shadow-md) !important;
}

.mud-drawer {
    background: var(--surface-color) !important;
    border-right: 1px solid var(--border-color) !important;
}

.drawer-header {
    padding: 2rem 1rem;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    margin-bottom: 1rem;
}

.custom-appbar {
    backdrop-filter: blur(10px);
}

.custom-drawer {
    box-shadow: var(--shadow-lg) !important;
}

.custom-drawer .mud-drawer-content {
    overflow-y: auto;
    max-height: 100vh;
    padding-bottom: 1rem;
    background-color: #374151 !important;
}

/* Ensure all drawer text is white */
.custom-drawer {
    background-color: #374151 !important;
}

.custom-drawer * {
    color: white !important;
}

.custom-drawer .mud-divider {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.custom-main-content {
    background: var(--background-color);
    min-height: calc(100vh - 64px);
}

.mud-nav-link {
    border-radius: var(--border-radius) !important;
    margin: 0.25rem 0.5rem !important;
    transition: var(--transition) !important;
}

.mud-nav-link:hover {
    background: var(--primary-color) !important;
    color: white !important;
    transform: translateX(4px) !important;
}

.mud-nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
    color: white !important;
}

/* Custom Navigation Styles */
.custom-nav-menu {
    padding: 1rem 0;
}

.compact-nav {
    padding: 0.5rem 0;
}

.compact-nav .mud-nav-link {
    color: white !important;
}

.compact-nav .mud-nav-link .mud-icon-root {
    color: white !important;
}

.nav-section-title {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 1rem;
    margin-bottom: 0.5rem;
}

/* Compact Navigation Links */
.nav-link-compact {
    padding: 0.4rem 1rem !important;
    margin: 0.1rem 0.5rem !important;
    border-radius: 8px !important;
    font-size: 0.875rem !important;
    min-height: auto !important;
    transition: all 0.2s ease !important;
    color: white !important;
}

.nav-link-compact .mud-icon-root {
    color: white !important;
}

.nav-link-compact:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(-2px);
    color: white !important;
}

.nav-link-compact:hover .mud-icon-root {
    color: white !important;
}

.nav-link-compact.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
    color: white !important;
}

.nav-link-compact.active .mud-icon-root {
    color: white !important;
}

/* Compact Chip */
.compact-chip {
    font-size: 0.7rem !important;
    height: 18px !important;
    padding: 0 6px !important;
    background-color: #10b981 !important;
    color: white !important;
}

/* Compact Logout Button */
.logout-form-compact {
    margin: 0.1rem 0.5rem;
}

.logout-button-compact {
    width: 100%;
    padding: 0.4rem 1rem;
    background: none;
    border: none;
    color: white !important;
    font-size: 0.875rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    text-align: right;
    direction: rtl;
}

.logout-button-compact .mud-icon-root {
    color: white !important;
}

.logout-button-compact:hover {
    background-color: rgba(239, 68, 68, 0.2);
    color: #ff6b6b !important;
}

.logout-button-compact:hover .mud-icon-root {
    color: #ff6b6b !important;
}

.nav-link-custom {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    color: var(--text-primary);
}

.nav-link-custom:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.nav-link-custom.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

/* User Profile Section */
.user-profile-section {
    padding: 0 0.5rem;
    margin-bottom: 1rem;
}

.user-profile-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: var(--border-radius);
}

/* Logout Button */
.logout-form {
    margin: 0.25rem 0.5rem;
}

.logout-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--border-radius);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.logout-button:hover {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    transform: translateX(4px);
}

/* Auth Section */
.auth-section {
    margin-top: 1rem;
}

/* Customer Dashboard Styles */
.customer-dashboard-header {
    margin-bottom: 2rem;
}

.action-card {
    transition: var(--transition);
    height: 100%;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.action-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
}

.btn-outline-custom {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    transition: var(--transition);
}

.btn-outline-custom:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.logout-btn {
    border-color: #ef4444;
    color: #ef4444;
}

.logout-btn:hover {
    background: #ef4444;
    color: white;
}

/* Orders Page Styles */
.orders-header {
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 1.25rem;
}

.stat-icon.pending {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.processing {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-icon.completed {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.total {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-icon.cancelled {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* Order Items */
.order-item {
    transition: var(--transition);
}

.order-item:hover {
    background: var(--background-color);
}

.order-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.order-icon.pending {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.order-icon.processing {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.order-icon.completed {
    background: linear-gradient(135deg, #10b981, #059669);
}

.order-icon.cancelled {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.empty-state {
    background: var(--background-color);
    border-radius: var(--border-radius);
    margin: 2rem;
}

/* Products Page Styles */
.products-header {
    margin-bottom: 2rem;
}

.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.product-card .mud-card-content {
    flex-grow: 1;
}

.product-name {
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.3;
}

.product-description {
    min-height: 3rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-error {
    color: #ef4444 !important;
    font-weight: 600;
}

/* Product Card Media */
.product-card .mud-card-media {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Product Actions */
.product-card .mud-card-actions {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    background: var(--background-color);
}

.product-card .mud-card-actions .mud-button {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Product Dialog */
.product-dialog .mud-dialog-content {
    padding: 1.5rem;
}

.product-dialog .mud-dialog-actions {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Responsive Product Grid */
@media (max-width: 768px) {
    .product-card {
        margin-bottom: 1rem;
    }

    .product-card .mud-card-actions {
        padding: 0.75rem;
    }

    .product-card .mud-card-actions .mud-button {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        margin: 0.1rem;
    }

    .products-header {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .product-card .mud-card-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .product-card .mud-card-actions .mud-button {
        width: 100%;
        justify-content: center;
    }
}

/* Shopping Cart Styles */
.cart-item {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.cart-item:hover {
    box-shadow: var(--shadow-sm);
}

.cart-item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.quick-product-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.quick-product-card:hover {
    box-shadow: var(--shadow-sm);
}

.quick-product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

/* Cart Responsive */
@media (max-width: 768px) {
    .cart-item {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }

    .cart-item .d-flex:last-child {
        width: 100%;
        justify-content: space-between;
    }

    .quick-product-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .quick-product-image {
        margin: 0 auto 0.5rem;
    }
}

/* Advanced Animations */
.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s infinite;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes glow {
    from { box-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color), 0 0 15px var(--primary-color); }
    to { box-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.02);
}

.hover-glow {
    transition: var(--transition);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* Gradient Backgrounds */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
}

.gradient-accent {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
}

.gradient-rainbow {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Glass Morphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Neumorphism Effect */
.neumorphism {
    background: var(--background-color);
    border-radius: var(--border-radius-lg);
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.6),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.neumorphism-inset {
    background: var(--background-color);
    border-radius: var(--border-radius-lg);
    box-shadow:
        inset 8px 8px 16px rgba(163, 177, 198, 0.6),
        inset -8px -8px 16px rgba(255, 255, 255, 0.8);
}

/* Text Effects */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.text-glow {
    text-shadow: 0 0 10px var(--primary-color);
}

/* Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.progress-dots {
    display: flex;
    gap: 0.5rem;
}

.progress-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: dotPulse 1.4s infinite ease-in-out;
}

.progress-dot:nth-child(1) { animation-delay: -0.32s; }
.progress-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes dotPulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Floating Elements */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0% { transform: translate(0, 0px); }
    50% { transform: translate(0, -10px); }
    100% { transform: translate(0, 0px); }
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Form Improvements */
.mud-input-control {
    border-radius: var(--border-radius) !important;
}

.mud-input-control:focus-within {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from { 
        opacity: 0;
        transform: scale(0.9);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .custom-card-content {
        padding: 1.5rem;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 2.25rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .drawer-header {
        padding: 1.5rem 1rem;
    }

    .feature-card {
        padding: 1.75rem;
        margin-bottom: 1rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .custom-card-content {
        padding: 1rem;
    }

    .feature-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    .custom-card-header {
        padding: 1rem;
    }

    .order-item .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }

    .order-item .text-center {
        text-align: left !important;
        width: 100%;
    }

    .nav-section-title {
        font-size: 0.75rem;
    }

    .user-profile-card {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 2rem 1rem;
        margin-bottom: 1rem;
    }

    .hero-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .hero-subtitle {
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
    }

    .custom-card-content {
        padding: 0.75rem;
    }

    .feature-card {
        padding: 1rem;
        text-align: center;
    }

    .feature-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }

    .order-icon {
        width: 2.5rem;
        height: 2.5rem;
        margin-right: 0.75rem;
    }

    .drawer-header {
        padding: 1rem 0.75rem;
    }

    .nav-link-custom {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .logout-button {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .btn-primary-custom,
    .btn-secondary-custom {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .action-card .custom-card-content {
        padding: 1rem;
    }

    .action-icon {
        width: 3rem;
        height: 3rem;
        margin-bottom: 0.75rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.5rem;
    }

    .hero-subtitle {
        font-size: 0.8rem;
    }

    .custom-main-content {
        padding: 0.5rem !important;
    }

    .feature-card,
    .stat-card {
        margin-bottom: 0.75rem;
    }

    .order-item {
        padding: 0.75rem !important;
    }

    .empty-state {
        margin: 1rem;
        padding: 2rem 1rem !important;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-glow:hover {
        transform: none;
        box-shadow: var(--shadow-md);
    }

    .nav-link-custom:hover {
        transform: none;
    }

    .feature-card:hover,
    .custom-card:hover,
    .action-card:hover {
        transform: none;
    }

    .btn-primary-custom:hover,
    .btn-secondary-custom:hover {
        transform: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .feature-icon,
    .stat-icon,
    .order-icon {
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #1f2937;
        --surface-color: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --border-color: #4b5563;
    }

    .glass {
        background: rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .neumorphism {
        background: #2d3748;
        box-shadow:
            8px 8px 16px rgba(0, 0, 0, 0.3),
            -8px -8px 16px rgba(255, 255, 255, 0.05);
    }
}

/* Print Styles */
@media print {
    .hero-section,
    .feature-card,
    .custom-card-header {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .fade-in,
    .slide-up,
    .scale-in {
        animation: none !important;
    }

    .mud-drawer,
    .mud-appbar {
        display: none !important;
    }

    .custom-main-content {
        margin: 0 !important;
        padding: 1rem !important;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-success {
    background: #dcfce7;
    color: #166534;
}

.status-warning {
    background: #fef3c7;
    color: #92400e;
}

.status-error {
    background: #fee2e2;
    color: #991b1b;
}

.status-info {
    background: #dbeafe;
    color: #1e40af;
}
